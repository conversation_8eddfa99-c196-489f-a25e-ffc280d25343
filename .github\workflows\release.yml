name: Release

on:
  push:
    tags:
      - 'v*'

env:
  ENABLE_UPX: 1

jobs:
  release:
    name: Release Binary
    runs-on: ubuntu-latest
    container:
      image: goreleaser/goreleaser-cross:v1.24
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - run: git config --global --add safe.directory "$(pwd)"

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: '^1.24'

      - name: Cache go module
        uses: actions/cache@v3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Install UPX
        uses: crazy-max/ghaction-upx@v3
        with:
          install-only: true

      - name: Run GoReleaser
        run: goreleaser release --clean
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ENABLE_UPX: true
