# Chatlog 免责声明

## 1. 定义

在本免责声明中，除非上下文另有说明，下列术语应具有以下含义：

- **"本项目"或"Chatlog"**：指本开源软件项目，包括其源代码、可执行程序、文档及相关资源。
- **"开发者"**：指本项目的创建者、维护者及代码贡献者。
- **"用户"**：指下载、安装、使用或以任何方式接触本项目的个人或实体。
- **"聊天数据"**：指通过各类即时通讯软件生成的对话内容及相关元数据。
- **"合法授权"**：指根据适用法律法规，由数据所有者或数据主体明确授予的处理其聊天数据的权限。
- **"第三方服务"**：指由非本项目开发者提供的外部服务，如大型语言模型(LLM) API 服务。

## 2. 使用目的与法律遵守

本项目仅供学习、研究和个人合法使用。用户须严格遵守所在国家/地区的法律法规使用本工具。任何违反法律法规、侵犯他人合法权益的行为，均与本项目及其开发者无关，相关法律责任由用户自行承担。

⚠️ **用户应自行了解并遵守当地有关数据访问、隐私保护、计算机安全和网络安全的法律法规。不同司法管辖区对数据处理有不同的法律要求，用户有责任确保其使用行为符合所有适用法规。**

## 3. 授权范围与隐私保护

- 本工具仅限于处理用户自己合法拥有的聊天数据，或已获得数据所有者明确授权的数据。
- 严禁将本工具用于未经授权获取、查看或分析他人聊天记录，或侵犯他人隐私权。
- 用户应采取适当措施保护通过本工具获取和处理的聊天数据安全，包括但不限于加密存储、限制访问权限、定期删除不必要数据等。
- 用户应确保其处理的聊天数据符合相关数据保护法规，包括但不限于获得必要的同意、保障数据主体权利、遵守数据最小化原则等。

## 4. 使用限制

- 本项目仅允许在合法授权情况下对聊天数据库进行备份与查看。
- 未经明确授权，严禁将本项目用于访问、查看、分析或处理任何第三方聊天数据。
- 使用第三方 LLM 服务时，用户应遵守相关服务提供商的服务条款和使用政策。
- 用户不得规避本项目中的任何技术限制，或尝试反向工程、反编译或反汇编本项目，除非适用法律明确允许此类活动。

## 5. 技术风险声明

⚠️ **使用本项目存在以下技术风险，用户应充分了解并自行承担：**

- 本工具需要访问聊天软件的数据库文件，可能因聊天软件版本更新导致功能失效或数据不兼容。
- 在 macOS 系统上使用时，需要临时关闭 SIP 安全机制，这可能降低系统安全性，用户应了解相关风险并自行决定是否使用。
- 本项目可能存在未知的技术缺陷或安全漏洞，可能导致数据损坏、丢失或泄露。
- 使用本项目处理大量数据可能导致系统性能下降或资源占用过高。
- 第三方依赖库或 API 的变更可能影响本项目的功能或安全性。

## 6. 禁止非法用途

严禁将本项目用于以下用途：

- 从事任何形式的非法活动，包括但不限于未授权系统测试、网络渗透或其他违反法律法规的行为。
- 监控、窃取或未经授权获取他人聊天记录或个人信息。
- 将获取的数据用于骚扰、诈骗、敲诈、威胁或其他侵害他人合法权益的行为。
- 规避任何安全措施或访问控制机制。
- 传播虚假信息、仇恨言论或违反公序良俗的内容。
- 侵犯任何第三方的知识产权、隐私权或其他合法权益。

**违反上述规定的，用户应自行承担全部法律责任，并赔偿因此给开发者或第三方造成的全部损失。**

## 7. 第三方服务集成

- 用户将聊天数据与第三方 LLM 服务（如 OpenAI、Claude 等）结合使用时，应仔细阅读并遵守这些服务的使用条款、隐私政策和数据处理协议。
- 用户应了解，向第三方服务传输数据可能导致数据离开用户控制范围，并受第三方服务条款约束。
- 本项目开发者不对第三方服务的可用性、安全性、准确性或数据处理行为负责，用户应自行评估相关风险。
- 用户应确保其向第三方服务传输数据的行为符合适用的数据保护法规和第三方服务条款。

## 8. 责任限制

**在法律允许的最大范围内：**

- 本项目按"原样"和"可用"状态提供，不对功能的适用性、可靠性、准确性、完整性或及时性做任何明示或暗示的保证。
- 开发者明确否认对适销性、特定用途适用性、不侵权以及任何其他明示或暗示的保证。
- 本项目开发者和贡献者不对用户使用本工具的行为及后果承担任何法律责任。
- 对于因使用本工具而可能导致的任何直接、间接、附带、特殊、惩罚性或后果性损失，包括但不限于数据丢失、业务中断、隐私泄露、声誉损害、利润损失、法律纠纷等，本项目开发者概不负责，即使开发者已被告知此类损失的可能性。
- 在任何情况下，开发者对用户的全部责任累计不超过用户为获取本软件实际支付的金额（如为免费获取则为零）。

## 9. 知识产权声明

- 本项目基于 Apache-2.0 许可证开源，用户在使用、修改和分发时应严格遵守该许可证的所有条款。
- 本项目的名称"Chatlog"、相关标识及商标权（如有）归开发者所有，未经明确授权，用户不得以任何方式使用这些标识进行商业活动。
- 根据 Apache-2.0 许可证，用户可自由使用、修改和分发本项目代码，但须遵守许可证规定的归属声明等要求。
- 用户对其修改版本自行承担全部责任，且不得以原项目名义发布，必须明确标明其为修改版本并与原项目区分。
- 用户不得移除或更改本项目中的版权声明、商标或其他所有权声明。

## 10. 数据处理合规性

- 用户在使用本项目处理个人数据时，应遵守适用的数据保护法规，包括但不限于《中华人民共和国个人信息保护法》、《通用数据保护条例》(GDPR)等。
- 用户应确保其具有处理相关数据的合法依据，如获得数据主体的明确同意。
- 用户应实施适当的技术和组织措施，确保数据安全，防止未授权访问、意外丢失或泄露。
- 在跨境传输数据时，用户应确保符合相关法律对数据出境的要求。
- 用户应尊重数据主体权利，包括访问权、更正权、删除权等。

## 11. 免责声明接受

下载、安装、使用本项目，表示用户已阅读、理解并同意遵守本免责声明的所有条款。如不同意，请立即停止使用本工具并删除相关代码和程序。

**用户确认：**
- 已完整阅读并理解本免责声明的全部内容
- 自愿接受本免责声明的全部条款
- 具有完全民事行为能力，能够理解并承担使用本项目的风险和责任
- 将遵守本免责声明中规定的所有义务和限制

## 12. 免责声明修改与通知

- 本免责声明可能根据项目发展和法律法规变化进行修改和调整，修改后的声明将在项目官方仓库页面公布。
- 开发者没有义务个别通知用户免责声明的变更，用户应定期查阅最新版本。
- 重大变更将通过项目仓库的 Release Notes 或 README 文件更新进行通知。
- 在免责声明更新后继续使用本项目，即视为接受修改后的条款。

## 13. 法律适用与管辖

- 本免责声明受中华人民共和国法律管辖，并按其解释。
- 任何与本免责声明有关的争议，应首先通过友好协商解决；协商不成的，提交至本项目开发者所在地有管辖权的人民法院诉讼解决。
- 对于中国境外用户，如本免责声明与用户所在地强制性法律规定冲突，应以不违反该强制性规定的方式解释和适用本声明，但本声明的其余部分仍然有效。

## 14. 可分割性

如本免责声明中的任何条款被有管辖权的法院或其他权威机构认定为无效、不合法或不可执行，不影响其余条款的有效性和可执行性。无效条款应被视为从本声明中分割，并在法律允许的最大范围内由最接近原条款意图的有效条款替代。

## 15. 完整协议

本免责声明构成用户与开发者之间关于本项目使用的完整协议，取代先前或同时期关于本项目的所有口头或书面协议、提议和陈述。本声明的任何豁免、修改或补充均应以书面形式作出并经开发者签署方为有效。


