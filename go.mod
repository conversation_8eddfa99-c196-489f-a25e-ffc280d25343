module github.com/sjzar/chatlog

go 1.24.0

require (
	github.com/Eyevinn/mp4ff v0.49.0
	github.com/fsnotify/fsnotify v1.9.0
	github.com/gdamore/tcell/v2 v2.8.1
	github.com/gin-gonic/gin v1.10.0
	github.com/google/uuid v1.6.0
	github.com/klauspost/compress v1.18.0
	github.com/mattn/go-sqlite3 v1.14.27
	github.com/pierrec/lz4/v4 v4.1.22
	github.com/rivo/tview v0.0.0-20250330220935-949945f8d922
	github.com/rs/zerolog v1.34.0
	github.com/shirou/gopsutil/v4 v4.25.3
	github.com/sirupsen/logrus v1.9.3
	github.com/sjzar/go-lame v0.0.8
	github.com/sjzar/go-silk v0.0.1
	github.com/spf13/cobra v1.9.1
	github.com/spf13/viper v1.20.1
	golang.org/x/crypto v0.37.0
	golang.org/x/sys v0.32.0
	google.golang.org/protobuf v1.36.6
	howett.net/plist v1.0.1
)

require (
	github.com/bytedance/sonic v1.13.2 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/ebitengine/purego v0.8.2 // indirect
	github.com/gabriel-vasile/mimetype v1.4.8 // indirect
	github.com/gdamore/encoding v1.0.1 // indirect
	github.com/gin-contrib/sse v1.1.0 // indirect
	github.com/git-jiadong/go-lame v0.0.0-20241215065806-************ // indirect
	github.com/git-jiadong/go-silk v0.0.0-20241215085148-b8734e30c24b // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.26.0 // indirect
	github.com/go-viper/mapstructure/v2 v2.2.1 // indirect
	github.com/goccy/go-json v0.10.5 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/cpuid/v2 v2.2.10 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/lucasb-eyer/go-colorful v1.2.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20250317134145-8bc96cf8fc35 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.16 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/pelletier/go-toml/v2 v2.2.4 // indirect
	github.com/power-devops/perfstat v0.0.0-20240221224432-82ca36839d55 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/sagikazarmark/locafero v0.9.0 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spf13/afero v1.14.0 // indirect
	github.com/spf13/cast v1.7.1 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tklauser/go-sysconf v0.3.15 // indirect
	github.com/tklauser/numcpus v0.10.0 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/arch v0.16.0 // indirect
	golang.org/x/net v0.39.0 // indirect
	golang.org/x/term v0.31.0 // indirect
	golang.org/x/text v0.24.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
