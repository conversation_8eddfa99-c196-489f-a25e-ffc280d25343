// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: packedinfo.proto

package wxproto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PackedInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          uint32                 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`       // 始终为 106 (0x6a)
	Version       uint32                 `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"` // 始终为 14 (0xe)
	Image         *ImageHash             `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`      // 图片哈希
	Video         *VideoHash             `protobuf:"bytes,4,opt,name=video,proto3" json:"video,omitempty"`      // 视频哈希
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PackedInfo) Reset() {
	*x = PackedInfo{}
	mi := &file_packedinfo_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackedInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackedInfo) ProtoMessage() {}

func (x *PackedInfo) ProtoReflect() protoreflect.Message {
	mi := &file_packedinfo_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackedInfo.ProtoReflect.Descriptor instead.
func (*PackedInfo) Descriptor() ([]byte, []int) {
	return file_packedinfo_proto_rawDescGZIP(), []int{0}
}

func (x *PackedInfo) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *PackedInfo) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *PackedInfo) GetImage() *ImageHash {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *PackedInfo) GetVideo() *VideoHash {
	if x != nil {
		return x.Video
	}
	return nil
}

type ImageHash struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Md5           string                 `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"` // 32 字符的 MD5 哈希
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImageHash) Reset() {
	*x = ImageHash{}
	mi := &file_packedinfo_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImageHash) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageHash) ProtoMessage() {}

func (x *ImageHash) ProtoReflect() protoreflect.Message {
	mi := &file_packedinfo_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageHash.ProtoReflect.Descriptor instead.
func (*ImageHash) Descriptor() ([]byte, []int) {
	return file_packedinfo_proto_rawDescGZIP(), []int{1}
}

func (x *ImageHash) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

type VideoHash struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Md5           string                 `protobuf:"bytes,8,opt,name=md5,proto3" json:"md5,omitempty"` // 32 字符的 MD5 哈希
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoHash) Reset() {
	*x = VideoHash{}
	mi := &file_packedinfo_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoHash) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoHash) ProtoMessage() {}

func (x *VideoHash) ProtoReflect() protoreflect.Message {
	mi := &file_packedinfo_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoHash.ProtoReflect.Descriptor instead.
func (*VideoHash) Descriptor() ([]byte, []int) {
	return file_packedinfo_proto_rawDescGZIP(), []int{2}
}

func (x *VideoHash) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

var File_packedinfo_proto protoreflect.FileDescriptor

var file_packedinfo_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x22, 0x98, 0x01, 0x0a, 0x0a, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x48, 0x61, 0x73, 0x68, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x05,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x48, 0x61, 0x73, 0x68, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x22, 0x1d, 0x0a, 0x09, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x48, 0x61, 0x73, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x22, 0x1d, 0x0a, 0x09, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x48, 0x61, 0x73, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x42, 0x0b, 0x5a, 0x09, 0x2e, 0x3b, 0x77,
	0x78, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_packedinfo_proto_rawDescOnce sync.Once
	file_packedinfo_proto_rawDescData []byte
)

func file_packedinfo_proto_rawDescGZIP() []byte {
	file_packedinfo_proto_rawDescOnce.Do(func() {
		file_packedinfo_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_packedinfo_proto_rawDesc), len(file_packedinfo_proto_rawDesc)))
	})
	return file_packedinfo_proto_rawDescData
}

var file_packedinfo_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_packedinfo_proto_goTypes = []any{
	(*PackedInfo)(nil), // 0: app.protobuf.PackedInfo
	(*ImageHash)(nil),  // 1: app.protobuf.ImageHash
	(*VideoHash)(nil),  // 2: app.protobuf.VideoHash
}
var file_packedinfo_proto_depIdxs = []int32{
	1, // 0: app.protobuf.PackedInfo.image:type_name -> app.protobuf.ImageHash
	2, // 1: app.protobuf.PackedInfo.video:type_name -> app.protobuf.VideoHash
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_packedinfo_proto_init() }
func file_packedinfo_proto_init() {
	if File_packedinfo_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_packedinfo_proto_rawDesc), len(file_packedinfo_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_packedinfo_proto_goTypes,
		DependencyIndexes: file_packedinfo_proto_depIdxs,
		MessageInfos:      file_packedinfo_proto_msgTypes,
	}.Build()
	File_packedinfo_proto = out.File
	file_packedinfo_proto_goTypes = nil
	file_packedinfo_proto_depIdxs = nil
}
