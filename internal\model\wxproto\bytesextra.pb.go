// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: bytesextra.proto

package wxproto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BytesExtraHeader struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field1        int32                  `protobuf:"varint,1,opt,name=field1,proto3" json:"field1,omitempty"`
	Field2        int32                  `protobuf:"varint,2,opt,name=field2,proto3" json:"field2,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BytesExtraHeader) Reset() {
	*x = BytesExtraHeader{}
	mi := &file_bytesextra_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BytesExtraHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BytesExtraHeader) ProtoMessage() {}

func (x *BytesExtraHeader) ProtoReflect() protoreflect.Message {
	mi := &file_bytesextra_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BytesExtraHeader.ProtoReflect.Descriptor instead.
func (*BytesExtraHeader) Descriptor() ([]byte, []int) {
	return file_bytesextra_proto_rawDescGZIP(), []int{0}
}

func (x *BytesExtraHeader) GetField1() int32 {
	if x != nil {
		return x.Field1
	}
	return 0
}

func (x *BytesExtraHeader) GetField2() int32 {
	if x != nil {
		return x.Field2
	}
	return 0
}

type BytesExtraItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BytesExtraItem) Reset() {
	*x = BytesExtraItem{}
	mi := &file_bytesextra_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BytesExtraItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BytesExtraItem) ProtoMessage() {}

func (x *BytesExtraItem) ProtoReflect() protoreflect.Message {
	mi := &file_bytesextra_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BytesExtraItem.ProtoReflect.Descriptor instead.
func (*BytesExtraItem) Descriptor() ([]byte, []int) {
	return file_bytesextra_proto_rawDescGZIP(), []int{1}
}

func (x *BytesExtraItem) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BytesExtraItem) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type BytesExtra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *BytesExtraHeader      `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Items         []*BytesExtraItem      `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BytesExtra) Reset() {
	*x = BytesExtra{}
	mi := &file_bytesextra_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BytesExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BytesExtra) ProtoMessage() {}

func (x *BytesExtra) ProtoReflect() protoreflect.Message {
	mi := &file_bytesextra_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BytesExtra.ProtoReflect.Descriptor instead.
func (*BytesExtra) Descriptor() ([]byte, []int) {
	return file_bytesextra_proto_rawDescGZIP(), []int{2}
}

func (x *BytesExtra) GetHeader() *BytesExtraHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *BytesExtra) GetItems() []*BytesExtraItem {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_bytesextra_proto protoreflect.FileDescriptor

var file_bytesextra_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x62, 0x79, 0x74, 0x65, 0x73, 0x65, 0x78, 0x74, 0x72, 0x61, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x22, 0x42, 0x0a, 0x10, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x31, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x31, 0x12, 0x16, 0x0a, 0x06,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x32, 0x22, 0x3a, 0x0a, 0x0e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x78, 0x0a, 0x0a, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x36,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x42, 0x0b, 0x5a, 0x09, 0x2e, 0x3b,
	0x77, 0x78, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_bytesextra_proto_rawDescOnce sync.Once
	file_bytesextra_proto_rawDescData []byte
)

func file_bytesextra_proto_rawDescGZIP() []byte {
	file_bytesextra_proto_rawDescOnce.Do(func() {
		file_bytesextra_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_bytesextra_proto_rawDesc), len(file_bytesextra_proto_rawDesc)))
	})
	return file_bytesextra_proto_rawDescData
}

var file_bytesextra_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_bytesextra_proto_goTypes = []any{
	(*BytesExtraHeader)(nil), // 0: app.protobuf.BytesExtraHeader
	(*BytesExtraItem)(nil),   // 1: app.protobuf.BytesExtraItem
	(*BytesExtra)(nil),       // 2: app.protobuf.BytesExtra
}
var file_bytesextra_proto_depIdxs = []int32{
	0, // 0: app.protobuf.BytesExtra.header:type_name -> app.protobuf.BytesExtraHeader
	1, // 1: app.protobuf.BytesExtra.items:type_name -> app.protobuf.BytesExtraItem
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_bytesextra_proto_init() }
func file_bytesextra_proto_init() {
	if File_bytesextra_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_bytesextra_proto_rawDesc), len(file_bytesextra_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_bytesextra_proto_goTypes,
		DependencyIndexes: file_bytesextra_proto_depIdxs,
		MessageInfos:      file_bytesextra_proto_msgTypes,
	}.Build()
	File_bytesextra_proto = out.File
	file_bytesextra_proto_goTypes = nil
	file_bytesextra_proto_depIdxs = nil
}
